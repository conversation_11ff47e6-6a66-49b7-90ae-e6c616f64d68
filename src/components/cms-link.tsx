"use client";

import Link from "next/link";
import type { FC } from "react";
import type { Page } from "@/payload-types";

import { cn } from "@/lib/utils";
import { useTransitionNavigation } from "@/lib/transitions";

type CMSLinkType = {
	appearance?: "inline" | "default" | "inline-button";
	children?: React.ReactNode;
	className?: string;
	label?: string | null;
	newTab?: boolean | null;
	reference?: {
		relationTo: "pages" | "posts";
		value: Page | string | number;
	} | null;
	type?: "custom" | "reference" | null;
	url?: string | null;
};

export const CMSLink: FC<CMSLinkType> = (props) => {
	const router = useTransitionNavigation();

	const {
		type,
		appearance = "inline",
		children,
		className,
		label,
		newTab,
		reference,
		url,
	} = props;

	const href =
		type === "reference" &&
		typeof reference?.value === "object" &&
		reference.value.slug
			? `${reference?.relationTo !== "pages" ? `/${reference?.relationTo}` : ""}/${
					reference.value.slug
				}`
			: url;

	if (!href) return null;

	const isExternal = href.startsWith("http");

	const newTabProps = newTab
		? { rel: "noopener noreferrer", target: "_blank" }
		: {};

	const onClickProps = !isExternal
		? {
				onClick: (e: React.MouseEvent) => {
					e.preventDefault();
					router.push(href);
				},
			}
		: {};

	if (appearance === "inline-button") {
		return (
			<Link
				href={href || url || ""}
				{...newTabProps}
				{...onClickProps}
				className="inline-button | inline-flex items-center gap-4 text-3xl font-bold px-10 py-4 bg-contrast text-secondary rounded-full -translate-y-[1.5vw]"
			>
				<svg
					width="26"
					height="26"
					viewBox="0 0 26 26"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Arrow right up</title>
					<path
						d="M2 0V4H19.18L0 23.18L2.82 26L22 6.82V24H26V0H2Z"
						fill="currentColor"
					/>
				</svg>
				{label && label}
			</Link>
		);
	}

	if (appearance === "inline") {
		return (
			<Link
				className={cn(className)}
				href={href || url || ""}
				{...onClickProps}
				{...newTabProps}
			>
				{label && label}
				{children && children}
			</Link>
		);
	}

	return (
		<Link
			className={cn(className, `variant--${appearance}`)}
			href={href || url || ""}
			{...onClickProps}
			{...newTabProps}
		>
			{label && label}
			{children && children}
		</Link>
	);
};
