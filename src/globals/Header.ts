import type { GlobalConfig } from "payload";
import { link } from "@/fields/link";

export const Header: GlobalConfig = {
	slug: "header",
	access: {
		read: () => true,
	},
	fields: [
		{
			name: "navItems",
			type: "array",
			maxRows: 6,
			admin: {
				initCollapsed: true,
				components: {
					RowLabel: "@/globals/RowLabel#RowLabel",
				},
			},
			fields: [
				{
					type: "row",
					fields: [
						{
							name: "isMegaNav",
							label: "Is Mega Nav?",
							type: "checkbox",
							defaultValue: false,
						},
					],
				},
				link({
					appearances: false,
					overrides: {
						admin: {
							condition: (_, siblingData) => siblingData.isMegaNav !== true,
						},
					},
				}),
				{
					name: "label",
					type: "text",
					admin: {
						condition: (_, siblingData) => siblingData.isMegaNav === true,
					},
				},
				{
					name: "children",
					label: "Children",
					type: "array",
					admin: {
						condition: (_, siblingData) => siblingData.isMegaNav === true,
					},
					fields: [link({ appearances: false })],
				},
			],
		},
	],
};
