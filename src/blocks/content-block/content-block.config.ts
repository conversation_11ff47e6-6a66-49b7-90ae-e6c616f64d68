import type { Block, Field } from "payload";

import {
	FixedToolbarFeature,
	HeadingFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import { link } from "@/fields/link";
import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";

const columnFields: Field[] = [
	{
		name: "richText",
		type: "richText",
		editor: lexicalEditor({
			features: ({ rootFeatures }) => {
				return [
					...rootFeatures,
					HeadingFeature({ enabledHeadingSizes: ["h2", "h3", "h4"] }),
					FixedToolbarFeature(),
					InlineToolbarFeature(),
					FontColorFeature(),
				];
			},
		}),
		label: false,
	},
	{
		name: "width",
		type: "select",
		options: [
			{ label: "1 Columns", value: "1" },
			{ label: "2 Columns", value: "2" },
			{ label: "3 Columns", value: "3" },
			{ label: "4 Columns", value: "4" },
			{ label: "5 Columns", value: "5" },
			{ label: "6 Columns", value: "6" },
			{ label: "7 Columns", value: "7" },
			{ label: "8 Columns", value: "8" },
			{ label: "9 Columns", value: "9" },
			{ label: "10 Columns", value: "10" },
			{ label: "11 Columns", value: "11" },
			{ label: "12 Columns", value: "12" },
		],
	},
	{
		name: "enableLink",
		type: "checkbox",
	},
	link({
		overrides: {
			admin: {
				condition: (_data, siblingData) => {
					return Boolean(siblingData?.enableLink);
				},
			},
		},
	}),
];

export const ContentBlockConfig: Block = {
	slug: "content",
	interfaceName: "ContentBlock",
	fields: [
		{
			name: "indent",
			type: "select",
			defaultValue: "oneSixth",
			options: [
				{
					label: "None",
					value: "none",
				},
				{
					label: "One Sixth",
					value: "oneSixth",
				},
				{
					label: "One Third",
					value: "oneThird",
				},
				{
					label: "Half",
					value: "half",
				},
			],
		},
		{
			name: "columns",
			type: "array",
			admin: {
				initCollapsed: true,
			},
			fields: columnFields,
		},
	],
};
