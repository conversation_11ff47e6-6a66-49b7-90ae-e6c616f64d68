import { Media } from "@/components/render/render-media";
import { cn } from "@/lib/utils";
import type {
	Concert,
	ConcertArchiveBlock as IConcertArchiveBlockProps,
} from "@/payload-types";
import configPromise from "@payload-config";
import { getPayload } from "payload";
import type { FC } from "react";

const ConcertItem: FC<{ className?: string; concert: Concert }> = ({
	className,
	concert,
}) => {
	return (
		<div className={cn("", className)}>
			<Media
				resource={concert.image}
				className="aspect-video mb-8"
				imgClassName="object-cover h-full w-full"
			/>
			<div className="mb-8">
				<h3>
					<strong>{concert.title}</strong>
				</h3>
				<p className="h3">{concert.subline}</p>
			</div>
			<div className="grid [grid-template-columns:max-content_1fr] gap-x-4 gap-y-2">
				<p>
					<strong>WANN:</strong>
				</p>
				<p>{concert.formattedDateString}</p>
				<p>
					<strong>WO:</strong>
				</p>
				<p>{concert.where}</p>
			</div>
		</div>
	);
};

const pattern = [
	"col-start-5 col-span-8",
	"col-start-1 col-span-4",
	"col-start-5 col-span-4",
	"col-start-1 col-span-8",
	"col-start-9 col-span-4",
];

export const ConcertArchiveBlock: FC<IConcertArchiveBlockProps> = async ({
	limit: limitProp = 1,
	populateBy,
	selectedDocs,
}) => {
	const payload = await getPayload({ config: configPromise });

	if (populateBy === "upcoming") {
		const concert = await payload
			.find({
				collection: "concerts",
				depth: 1,
				where: {
					"dates.date": {
						greater_than_equal: new Date().toISOString(),
					},
				},
				sort: "dates.date",
				limit: 1,
			})
			.then((res) => res.docs[0]);

		if (!concert) {
			console.warn("No upcoming concert found");
			return null;
		}

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					<ConcertItem concert={concert} className="col-start-3 col-span-8" />
				</div>
			</section>
		);
	}

	if (populateBy === "selection") {
		if (!selectedDocs?.length) {
			console.warn("No concerts selected");
			return null;
		}

		const concerts = selectedDocs.map((post) => {
			if (typeof post.value === "object") return post.value;
		}) as Concert[];

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					{concerts.map((concert, index) => (
						<ConcertItem
							key={concert.id}
							concert={concert}
							className={pattern[index % pattern.length]}
						/>
					))}
				</div>
			</section>
		);
	}

	const concerts = await payload
		.find({
			collection: "concerts",
			depth: 1,
			sort: "dates.date",
			where: {
				"dates.date": {
					greater_than_equal: new Date().toISOString(),
				},
			},
			limit: limitProp || 3,
		})
		.then((res) => res.docs);

	return (
		<section className="layout-block">
			<div className="default-grid gap-y-24">
				{concerts.map((concert, index) => (
					<ConcertItem
						key={concert.id}
						concert={concert}
						className={pattern[index % pattern.length]}
					/>
				))}
			</div>
		</section>
	);
};
