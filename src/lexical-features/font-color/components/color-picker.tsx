"use client";

import "@/css/index.css";
import { ThemeColorsView } from "./views/theme-colors-view";
import { ColorPickerView } from "./views/color-picker-view";
import { useState } from "react";

type DropdownColorPickerProps = {
	fontColor?: string;
	onFontColorChange: (color: string, cssVariableColor?: string) => void;
	onApplyStyles: () => void;
};

export type ColorSpectrum = "hex" | "hsl" | "rgb";

const defaultColor = "#000000";

export const ColorPicker = ({
	fontColor = defaultColor,
	onFontColorChange,
	onApplyStyles,
}: DropdownColorPickerProps) => {
	const [colorSpectrum, setColorSpectrum] = useState<ColorSpectrum>("hex");

	return (
		<div className="flex">
			<ThemeColorsView
				colorSpectrum={colorSpectrum}
				onColorSpectrumChange={setColorSpectrum}
				onFontColorChange={onFontColorChange}
			/>

			<ColorPickerView
				onApplyStyles={onApplyStyles}
				fontColor={fontColor}
				onFontColorChange={onFontColorChange}
			/>
		</div>
	);
};
